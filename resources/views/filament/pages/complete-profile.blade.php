<x-filament-panels::page>
    <div class="max-w-2xl mx-auto">
        <div class="fi-section-content-ctn rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
            <div class="fi-section-content p-6">
                <div class="fi-section-header-ctn flex flex-col gap-3">
                    <h2 class="fi-section-header-heading text-base font-semibold leading-6 text-gray-950 dark:text-white">
                        Complete Your Profile
                    </h2>
                    <p class="fi-section-header-description text-sm text-gray-500 dark:text-gray-400">
                        Please complete your profile information to continue using the dashboard.
                    </p>
                </div>
                
                <form wire:submit="submit" class="mt-6">
                    <div class="space-y-6">
                        <div>
                            <label for="data.classroom" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Classroom</label>
                            <select wire:model="data.classroom" id="data.classroom" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
                                <option value="">Select Classroom</option>
                                <option value="X">X</option>
                                <option value="X A">X A</option>
                                <option value="X B">X B</option>
                                <option value="XI">XI</option>
                                <option value="XI A">XI A</option>
                                <option value="XI B">XI B</option>
                                <option value="XII">XII</option>
                                <option value="XII A">XII A</option>
                                <option value="XII B">XII B</option>
                            </select>
                            @error('data.classroom') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div>
                            <label for="data.major" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Major</label>
                            <select wire:model="data.major" id="data.major" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
                                <option value="">Select Major</option>
                                <option value="AKL">AKL</option>
                                <option value="DKV">DKV</option>
                                <option value="RPLG">RPLG</option>
                                <option value="TJKT">TJKT</option>
                            </select>
                            @error('data.major') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div>
                            <label for="data.phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Phone</label>
                            <input wire:model="data.phone" type="text" id="data.phone" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
                            @error('data.phone') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div>
                            <label for="data.address" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Address</label>
                            <textarea wire:model="data.address" id="data.address" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"></textarea>
                            @error('data.address') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>
                    </div>

                    <div class="fi-form-actions flex flex-wrap items-center gap-3 justify-start mt-6">
                        <x-filament::button type="submit">
                            Complete Profile
                        </x-filament::button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-filament-panels::page>
