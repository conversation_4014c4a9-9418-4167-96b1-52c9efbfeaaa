<?php

namespace App\Filament\Pages\Auth;

use App\Models\User;
use Filament\Pages\Auth\Register as BaseRegister;
use Illuminate\Auth\Events\Registered;

class Register extends BaseRegister
{
    protected function handleRegistration(array $data): User
    {
        $user = User::create($data);
        
        // Automatically assign customer role
        $user->assignRole('customer');
        
        event(new Registered($user));
        
        return $user;
    }
}
