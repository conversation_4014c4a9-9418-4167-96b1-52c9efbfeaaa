<?php

namespace App\Filament\Pages;

use App\Models\Customer;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Illuminate\Support\Facades\Auth;

class CompleteProfile extends Page implements HasForms, HasActions
{
    use InteractsWithForms, InteractsWithActions;

    protected static ?string $navigationIcon = 'heroicon-o-user';
    protected static string $view = 'filament.pages.complete-profile';
    protected static ?string $title = 'Complete Your Profile';
    protected static bool $shouldRegisterNavigation = false;
    
    public ?array $data = [];

    public function mount(): void
    {
        // Redirect if user already has completed profile
        if (Auth::user()->customer) {
            return redirect()->to('/dashboard');
        }
        
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('classroom')
                    ->label('Classroom')
                    ->options([
                        'X' => 'X',
                        'X A' => 'X A',
                        'X B' => 'X B',
                        'XI' => 'XI',
                        'XI A' => 'XI A',
                        'XI B' => 'XI B',
                        'XII' => 'XII',
                        'XII A' => 'XII A',
                        'XII B' => 'XII B',
                    ])
                    ->required(),
                Select::make('major')
                    ->label('Major')
                    ->options([
                        'AKL' => 'AKL',
                        'DKV' => 'DKV',
                        'RPLG' => 'RPLG',
                        'TJKT' => 'TJKT',
                    ])
                    ->required(),
                TextInput::make('phone')
                    ->label('Phone')
                    ->required(),
                TextInput::make('address')
                    ->label('Address')
                    ->required(),
            ])
            ->statePath('data');
    }

    protected function getFormActions(): array
    {
        return [
            Action::make('submit')
                ->label('Complete Profile')
                ->submit('submit'),
        ];
    }

    public function submit(): void
    {
        $data = $this->form->getState();
        
        Customer::create([
            'user_id' => Auth::id(),
            'classroom' => $data['classroom'],
            'major' => $data['major'],
            'phone' => $data['phone'],
            'address' => $data['address'],
        ]);

        Notification::make()
            ->title('Profile completed successfully!')
            ->success()
            ->send();

        return redirect()->to('/dashboard');
    }

    public static function canAccess(): bool
    {
        return Auth::user()?->hasRole('customer') && !Auth::user()->customer;
    }
}
