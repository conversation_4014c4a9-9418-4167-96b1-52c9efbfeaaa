<?php

namespace App\Filament\Pages;

use App\Models\Customer;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Support\Icons\Heroicon;
use Illuminate\Support\Facades\Auth;

class CompleteProfile extends Page
{

    protected static string|\BackedEnum|null $navigationIcon = Heroicon::OutlinedUser;
    protected string $view = 'filament.pages.complete-profile';
    protected static ?string $title = 'Complete Your Profile';
    protected static bool $shouldRegisterNavigation = false;

    public ?array $data = [];

    public function mount(): void
    {
        // Redirect if user already has completed profile
        if (Auth::user()->customer) {
            redirect()->to('/dashboard');
            return;
        }

        // Initialize empty data
        $this->data = [];
    }





    public function submit(): void
    {
        // Validate the form data
        $this->validate([
            'data.classroom' => 'required|string',
            'data.major' => 'required|string',
            'data.phone' => 'required|string',
            'data.address' => 'required|string',
        ]);

        Customer::create([
            'user_id' => Auth::id(),
            'classroom' => $this->data['classroom'],
            'major' => $this->data['major'],
            'phone' => $this->data['phone'],
            'address' => $this->data['address'],
        ]);

        Notification::make()
            ->title('Profile completed successfully!')
            ->success()
            ->send();

        redirect()->to('/dashboard');
    }

    public static function canAccess(): bool
    {
        return Auth::user()?->hasRole('customer') && !Auth::user()->customer;
    }
}
