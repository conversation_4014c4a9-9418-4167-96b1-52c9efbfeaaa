<?php

namespace App\Filament\Pages;

use App\Models\Customer;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Illuminate\Support\Facades\Auth;

class CompleteProfile extends Page implements HasForms
{
    use InteractsWithForms;

    protected static string|\BackedEnum|null $navigationIcon = Heroicon::OutlinedUser;
    protected string $view = 'filament.pages.complete-profile';
    protected static ?string $title = 'Complete Your Profile';
    protected static bool $shouldRegisterNavigation = false;

    public ?array $data = [];

    public function mount(): void
    {
        // Redirect if user already has completed profile
        if (Auth::user()->customer) {
            redirect()->to('/dashboard');
            return;
        }

        // Initialize empty data
        $this->data = [];
    }
    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('classroom')
                    ->label('Classroom')
                    ->options([
                        'X' => 'X',
                        'X A' => 'X A',
                        'X B' => 'X B',
                        'XI' => 'XI',
                        'XI A' => 'XI A',
                        'XI B' => 'XI B',
                        'XII' => 'XII',
                        'XII A' => 'XII A',
                        'XII B' => 'XII B',
                    ]),
                Select::make('major')
                    ->label('Major')
                    ->options([
                        'AKL' => 'AKL',
                        'DKV' => 'DKV',
                        'RPLG' => 'RPLG',
                        'TJKT' => 'TJKT',
                    ]),
                TextInput::make('phone')
                    ->label('Phone')
                    ->required(),
                TextInput::make('address')
                    ->label('Address')
                    ->required(),
            ])
            ->statePath('data');
    }





    public function submit(): void
    {
        $data = $this->form->getState();

        Customer::create([
            'user_id' => Auth::id(),
            'classroom' => $data['classroom'],
            'major' => $data['major'],
            'phone' => $data['phone'],
            'address' => $data['address'],
        ]);

        Notification::make()
            ->title('Profile completed successfully!')
            ->success()
            ->send();

        redirect()->to('/dashboard');
    }

    public static function canAccess(): bool
    {
        return Auth::user()?->hasRole('customer') && !Auth::user()->customer;
    }
}
