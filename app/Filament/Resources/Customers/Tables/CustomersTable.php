<?php

namespace App\Filament\Resources\Customers\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class CustomersTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('user.name')
                    ->label('Name'),
                TextColumn::make('classroom')
                    ->label('Classroom'),
                TextColumn::make('major')
                    ->label('Major'),
                TextColumn::make('status')
                    ->label('Status')
                    ->state(fn (Get $get) => ! empty($get('classroom')) ? 'Teacher' : 'Student'),
                TextColumn::make('Phone')
                    ->label('Phone'),
                TextColumn::make('Address')
                    ->label('Address'),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
