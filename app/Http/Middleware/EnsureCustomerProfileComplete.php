<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class EnsureCustomerProfileComplete
{
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::user();
        
        if ($user && $user->hasRole('customer') && !$user->customer) {
            // Allow access to complete-profile page and auth routes
            if (!str_contains($request->route()->getName() ?? '', 'complete-profile') && 
                !str_contains($request->route()->getName() ?? '', 'auth.')) {
                return redirect()->route('filament.admin.pages.complete-profile');
            }
        }
        
        return $next($request);
    }
}
