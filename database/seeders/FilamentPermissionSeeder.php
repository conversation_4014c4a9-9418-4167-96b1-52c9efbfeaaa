<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class FilamentPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Role::create(['name' => 'admin']);
        Role::create(['name' => 'seller']);
        Role::create(['name' => 'customer']);

        Permission::create(['name' => 'view_users']);
        Permission::create(['name' => 'edit_users']);
        Permission::create(['name' => 'delete_users']);

        Permission::create(['name' => 'view_customers']);
        Permission::create(['name' => 'edit_customers']);
        Permission::create(['name' => 'delete_customers']);

        $role = Role::findByName('admin');
        $role->givePermissionTo(['view_users', 'edit_users', 'delete_users', 'view_customers', 'edit_customers', 'delete_customers']);

        $role = Role::findByName('seller');
        $role->givePermissionTo(['view_users', 'view_customers']);

        $user = User::find(1);
        $user->assignRole('admin');
    }
}
